package com.calculator.model;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 计算器模型类，负责处理计算逻辑和状态管理
 */
public class Calculator {
    
    // 当前显示的数值
    private String currentDisplay = "0";
    
    // 存储的操作数
    private BigDecimal storedValue = BigDecimal.ZERO;
    
    // 当前操作符
    private String operator = "";
    
    // 是否等待新的操作数输入
    private boolean waitingForOperand = false;
    
    // 是否刚刚执行了计算
    private boolean justCalculated = false;
    
    // 内存存储值
    private BigDecimal memoryValue = BigDecimal.ZERO;
    
    // 最大显示位数
    private static final int MAX_DISPLAY_DIGITS = 15;
    
    /**
     * 输入数字
     */
    public void inputDigit(String digit) {
        if (waitingForOperand || justCalculated) {
            currentDisplay = digit;
            waitingForOperand = false;
            justCalculated = false;
        } else {
            if ("0".equals(currentDisplay)) {
                currentDisplay = digit;
            } else {
                if (currentDisplay.length() < MAX_DISPLAY_DIGITS) {
                    currentDisplay += digit;
                }
            }
        }
    }
    
    /**
     * 输入小数点
     */
    public void inputDecimal() {
        if (waitingForOperand || justCalculated) {
            currentDisplay = "0.";
            waitingForOperand = false;
            justCalculated = false;
        } else if (!currentDisplay.contains(".")) {
            currentDisplay += ".";
        }
    }
    
    /**
     * 输入操作符
     */
    public void inputOperator(String newOperator) {
        BigDecimal inputValue = parseCurrentDisplay();
        
        if (inputValue == null) return;
        
        if (!operator.isEmpty() && !waitingForOperand) {
            BigDecimal result = performCalculation(storedValue, inputValue, operator);
            if (result != null) {
                currentDisplay = formatResult(result);
                storedValue = result;
            }
        } else {
            storedValue = inputValue;
        }
        
        waitingForOperand = true;
        justCalculated = false;
        operator = newOperator;
    }
    
    /**
     * 执行计算
     */
    public void calculate() {
        BigDecimal inputValue = parseCurrentDisplay();
        
        if (inputValue == null || operator.isEmpty()) return;
        
        BigDecimal result = performCalculation(storedValue, inputValue, operator);
        if (result != null) {
            currentDisplay = formatResult(result);
            storedValue = result;
            operator = "";
            waitingForOperand = true;
            justCalculated = true;
        }
    }
    
    /**
     * 清除当前输入 (CE)
     */
    public void clearEntry() {
        currentDisplay = "0";
    }
    
    /**
     * 清除所有 (C)
     */
    public void clearAll() {
        currentDisplay = "0";
        storedValue = BigDecimal.ZERO;
        operator = "";
        waitingForOperand = false;
        justCalculated = false;
    }
    
    /**
     * 退格
     */
    public void backspace() {
        if (!waitingForOperand && !justCalculated) {
            if (currentDisplay.length() > 1) {
                currentDisplay = currentDisplay.substring(0, currentDisplay.length() - 1);
            } else {
                currentDisplay = "0";
            }
        }
    }
    
    /**
     * 切换正负号
     */
    public void toggleSign() {
        if (!"0".equals(currentDisplay)) {
            if (currentDisplay.startsWith("-")) {
                currentDisplay = currentDisplay.substring(1);
            } else {
                currentDisplay = "-" + currentDisplay;
            }
        }
    }
    
    /**
     * 百分比计算
     */
    public void percentage() {
        BigDecimal value = parseCurrentDisplay();
        if (value != null) {
            BigDecimal result = value.divide(new BigDecimal("100"), 10, RoundingMode.HALF_UP);
            currentDisplay = formatResult(result);
        }
    }
    
    /**
     * 内存清除 (MC)
     */
    public void memoryClear() {
        memoryValue = BigDecimal.ZERO;
    }
    
    /**
     * 内存读取 (MR)
     */
    public void memoryRecall() {
        currentDisplay = formatResult(memoryValue);
        waitingForOperand = false;
        justCalculated = false;
    }
    
    /**
     * 内存加法 (M+)
     */
    public void memoryAdd() {
        BigDecimal value = parseCurrentDisplay();
        if (value != null) {
            memoryValue = memoryValue.add(value);
        }
    }
    
    /**
     * 内存减法 (M-)
     */
    public void memorySubtract() {
        BigDecimal value = parseCurrentDisplay();
        if (value != null) {
            memoryValue = memoryValue.subtract(value);
        }
    }
    
    /**
     * 获取当前显示值
     */
    public String getCurrentDisplay() {
        return currentDisplay;
    }
    
    /**
     * 解析当前显示的字符串为BigDecimal
     */
    private BigDecimal parseCurrentDisplay() {
        try {
            return new BigDecimal(currentDisplay);
        } catch (NumberFormatException e) {
            currentDisplay = "错误";
            return null;
        }
    }
    
    /**
     * 执行具体的计算操作
     */
    private BigDecimal performCalculation(BigDecimal first, BigDecimal second, String op) {
        try {
            switch (op) {
                case "+":
                    return first.add(second);
                case "-":
                    return first.subtract(second);
                case "×":
                    return first.multiply(second);
                case "÷":
                    if (second.compareTo(BigDecimal.ZERO) == 0) {
                        currentDisplay = "错误";
                        return null;
                    }
                    return first.divide(second, 10, RoundingMode.HALF_UP);
                default:
                    return second;
            }
        } catch (ArithmeticException e) {
            currentDisplay = "错误";
            return null;
        }
    }
    
    /**
     * 格式化计算结果
     */
    private String formatResult(BigDecimal result) {
        // 移除尾随零
        result = result.stripTrailingZeros();
        
        String resultStr = result.toPlainString();
        
        // 如果结果太长，使用科学计数法
        if (resultStr.length() > MAX_DISPLAY_DIGITS) {
            return String.format("%.6E", result.doubleValue());
        }
        
        return resultStr;
    }
    
    /**
     * 检查是否有内存值
     */
    public boolean hasMemoryValue() {
        return memoryValue.compareTo(BigDecimal.ZERO) != 0;
    }
}
