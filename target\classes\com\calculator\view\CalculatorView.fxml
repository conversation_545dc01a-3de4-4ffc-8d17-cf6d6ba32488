<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.calculator.controller.CalculatorController">
   <children>
      <!-- 显示屏区域 -->
      <TextField fx:id="displayField" alignment="CENTER_RIGHT" editable="false" prefHeight="60.0" prefWidth="300.0" styleClass="display-field" text="0">
         <VBox.margin>
            <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
         </VBox.margin>
      </TextField>
      
      <!-- 按钮区域 -->
      <GridPane hgap="2.0" vgap="2.0">
         <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
         </columnConstraints>
         <rowConstraints>
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
         </rowConstraints>
         
         <!-- 第一行: CE C ← ÷ -->
         <Button fx:id="btnClearEntry" mnemonicParsing="false" onAction="#handleClearEntry" prefHeight="50.0" prefWidth="60.0" styleClass="function-button" text="CE" GridPane.columnIndex="0" GridPane.rowIndex="0" />
         <Button fx:id="btnClear" mnemonicParsing="false" onAction="#handleClear" prefHeight="50.0" prefWidth="60.0" styleClass="function-button" text="C" GridPane.columnIndex="1" GridPane.rowIndex="0" />
         <Button fx:id="btnBackspace" mnemonicParsing="false" onAction="#handleBackspace" prefHeight="50.0" prefWidth="60.0" styleClass="function-button" text="←" GridPane.columnIndex="2" GridPane.rowIndex="0" />
         <Button fx:id="btnDivide" mnemonicParsing="false" onAction="#handleOperator" prefHeight="50.0" prefWidth="60.0" styleClass="operator-button" text="÷" GridPane.columnIndex="3" GridPane.rowIndex="0" />
         
         <!-- 第二行: 7 8 9 × -->
         <Button fx:id="btn7" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="7" GridPane.columnIndex="0" GridPane.rowIndex="1" />
         <Button fx:id="btn8" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="8" GridPane.columnIndex="1" GridPane.rowIndex="1" />
         <Button fx:id="btn9" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="9" GridPane.columnIndex="2" GridPane.rowIndex="1" />
         <Button fx:id="btnMultiply" mnemonicParsing="false" onAction="#handleOperator" prefHeight="50.0" prefWidth="60.0" styleClass="operator-button" text="×" GridPane.columnIndex="3" GridPane.rowIndex="1" />
         
         <!-- 第三行: 4 5 6 - -->
         <Button fx:id="btn4" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="4" GridPane.columnIndex="0" GridPane.rowIndex="2" />
         <Button fx:id="btn5" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="5" GridPane.columnIndex="1" GridPane.rowIndex="2" />
         <Button fx:id="btn6" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="6" GridPane.columnIndex="2" GridPane.rowIndex="2" />
         <Button fx:id="btnSubtract" mnemonicParsing="false" onAction="#handleOperator" prefHeight="50.0" prefWidth="60.0" styleClass="operator-button" text="-" GridPane.columnIndex="3" GridPane.rowIndex="2" />
         
         <!-- 第四行: 1 2 3 + -->
         <Button fx:id="btn1" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="1" GridPane.columnIndex="0" GridPane.rowIndex="3" />
         <Button fx:id="btn2" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="2" GridPane.columnIndex="1" GridPane.rowIndex="3" />
         <Button fx:id="btn3" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="3" GridPane.columnIndex="2" GridPane.rowIndex="3" />
         <Button fx:id="btnAdd" mnemonicParsing="false" onAction="#handleOperator" prefHeight="50.0" prefWidth="60.0" styleClass="operator-button" text="+" GridPane.columnIndex="3" GridPane.rowIndex="3" />
         
         <!-- 第五行: ± 0 . = -->
         <Button fx:id="btnToggleSign" mnemonicParsing="false" onAction="#handleToggleSign" prefHeight="50.0" prefWidth="60.0" styleClass="function-button" text="±" GridPane.columnIndex="0" GridPane.rowIndex="4" />
         <Button fx:id="btn0" mnemonicParsing="false" onAction="#handleDigit" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="0" GridPane.columnIndex="1" GridPane.rowIndex="4" />
         <Button fx:id="btnDecimal" mnemonicParsing="false" onAction="#handleDecimal" prefHeight="50.0" prefWidth="60.0" styleClass="number-button" text="." GridPane.columnIndex="2" GridPane.rowIndex="4" />
         <Button fx:id="btnEquals" mnemonicParsing="false" onAction="#handleEquals" prefHeight="50.0" prefWidth="60.0" styleClass="operator-button" text="=" GridPane.columnIndex="3" GridPane.rowIndex="4" />
         
         <VBox.margin>
            <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
         </VBox.margin>
      </GridPane>
   </children>
</VBox>
