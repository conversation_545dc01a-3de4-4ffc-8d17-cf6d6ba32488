/* 计算器整体样式 */
.root {
    -fx-background-color: #f0f0f0;
    -fx-font-family: "Arial", "Microsoft YaHei", sans-serif;
}

/* 显示屏样式 */
.display-field {
    -fx-background-color: #ffffff;
    -fx-border-color: #cccccc;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-font-size: 24px;
    -fx-font-family: "Consolas", "Courier New", monospace;
    -fx-text-fill: #333333;
    -fx-padding: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.display-field:focused {
    -fx-border-color: #4CAF50;
}

/* 数字按钮样式 - 浅灰色 */
.number-button {
    -fx-background-color: #e8e8e8;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 1, 0, 0, 1);
    -fx-cursor: hand;
}

.number-button:hover {
    -fx-background-color: #d8d8d8;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

.number-button:pressed {
    -fx-background-color: #c8c8c8;
    -fx-effect: innershadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

/* 运算符按钮样式 - 橙色 */
.operator-button {
    -fx-background-color: #ff9500;
    -fx-border-color: #e6850e;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #ffffff;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 1, 0, 0, 1);
    -fx-cursor: hand;
}

.operator-button:hover {
    -fx-background-color: #e6850e;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

.operator-button:pressed {
    -fx-background-color: #cc7a0d;
    -fx-effect: innershadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

/* 功能按钮样式 - 深灰色 */
.function-button {
    -fx-background-color: #666666;
    -fx-border-color: #555555;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #ffffff;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 1, 0, 0, 1);
    -fx-cursor: hand;
}

.function-button:hover {
    -fx-background-color: #555555;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

.function-button:pressed {
    -fx-background-color: #444444;
    -fx-effect: innershadow(three-pass-box, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

/* 网格面板样式 */
.grid-pane {
    -fx-padding: 5px;
    -fx-background-color: #f0f0f0;
}

/* VBox容器样式 */
.vbox {
    -fx-background-color: #f0f0f0;
    -fx-padding: 10px;
    -fx-spacing: 5px;
}

/* 按钮通用样式调整 */
.button {
    -fx-min-width: 70px;
    -fx-min-height: 60px;
    -fx-max-width: 70px;
    -fx-max-height: 60px;
    -fx-pref-width: 70px;
    -fx-pref-height: 60px;
}

/* 焦点样式 */
.button:focused {
    -fx-border-color: #4CAF50;
    -fx-border-width: 2px;
}

/* 禁用状态样式 */
.button:disabled {
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

/* 特殊按钮样式调整 */
#btnEquals {
    -fx-background-color: #4CAF50;
    -fx-border-color: #45a049;
}

#btnEquals:hover {
    -fx-background-color: #45a049;
}

#btnEquals:pressed {
    -fx-background-color: #3d8b40;
}

/* 错误状态显示 */
.display-field.error {
    -fx-text-fill: #ff0000;
    -fx-border-color: #ff0000;
}

/* 动画效果 */
.button {
    -fx-transition: all 0.1s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 400px) {
    .button {
        -fx-min-width: 50px;
        -fx-min-height: 40px;
        -fx-font-size: 14px;
    }
    
    .display-field {
        -fx-font-size: 20px;
    }
}
