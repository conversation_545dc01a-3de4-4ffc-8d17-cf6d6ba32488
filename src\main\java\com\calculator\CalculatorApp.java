package com.calculator;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

/**
 * JavaFX计算器应用程序主类
 */
public class CalculatorApp extends Application {

    @Override
    public void start(Stage primaryStage) throws Exception {
        // 加载FXML文件
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/calculator/view/CalculatorView.fxml"));
        Parent root = loader.load();

        // 加载CSS样式
        Scene scene = new Scene(root, 300, 400);
        scene.getStylesheets().add(getClass().getResource("/styles/calculator.css").toExternalForm());

        // 设置窗口属性
        primaryStage.setTitle("JavaFX 计算器");
        primaryStage.setScene(scene);
        primaryStage.setResizable(false); // 禁止调整窗口大小
        
        // 设置窗口图标（如果有的话）
        try {
            primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/images/calculator-icon.png")));
        } catch (Exception e) {
            // 如果图标文件不存在，忽略错误
            System.out.println("Calculator icon not found, using default icon.");
        }
        
        // 设置窗口关闭时的行为
        primaryStage.setOnCloseRequest(event -> {
            System.out.println("计算器应用程序已关闭");
        });

        // 显示窗口
        primaryStage.show();
        
        // 确保窗口居中显示
        primaryStage.centerOnScreen();
        
        System.out.println("JavaFX 计算器应用程序已启动");
    }

    /**
     * 应用程序入口点
     */
    public static void main(String[] args) {
        // 设置系统属性以支持高DPI显示
        System.setProperty("prism.allowhidpi", "false");
        
        // 启动JavaFX应用程序
        launch(args);
    }
}
