package com.calculator.controller;

import com.calculator.model.Calculator;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyEvent;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 计算器控制器类，处理用户界面交互和业务逻辑
 */
public class CalculatorController implements Initializable {

    @FXML private TextField displayField;
    
    // 数字按钮
    @FXML private Button btn0, btn1, btn2, btn3, btn4, btn5, btn6, btn7, btn8, btn9;
    
    // 运算符按钮
    @FXML private Button btnAdd, btnSubtract, btnMultiply, btnDivide, btnEquals;
    
    // 功能按钮
    @FXML private Button btnClear, btnClearEntry, btnBackspace, btnDecimal, btnToggleSign;
    
    // 计算器模型
    private Calculator calculator;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        calculator = new Calculator();
        updateDisplay();
        
        // 设置键盘事件监听
        setupKeyboardHandling();
    }

    /**
     * 处理数字按钮点击
     */
    @FXML
    private void handleDigit(ActionEvent event) {
        Button button = (Button) event.getSource();
        String digit = button.getText();
        calculator.inputDigit(digit);
        updateDisplay();
    }

    /**
     * 处理运算符按钮点击
     */
    @FXML
    private void handleOperator(ActionEvent event) {
        Button button = (Button) event.getSource();
        String operator = button.getText();
        calculator.inputOperator(operator);
        updateDisplay();
    }

    /**
     * 处理等号按钮点击
     */
    @FXML
    private void handleEquals(ActionEvent event) {
        calculator.calculate();
        updateDisplay();
    }

    /**
     * 处理小数点按钮点击
     */
    @FXML
    private void handleDecimal(ActionEvent event) {
        calculator.inputDecimal();
        updateDisplay();
    }

    /**
     * 处理清除当前输入 (CE)
     */
    @FXML
    private void handleClearEntry(ActionEvent event) {
        calculator.clearEntry();
        updateDisplay();
    }

    /**
     * 处理清除所有 (C)
     */
    @FXML
    private void handleClear(ActionEvent event) {
        calculator.clearAll();
        updateDisplay();
        // 移除错误样式
        displayField.getStyleClass().remove("error");
    }

    /**
     * 处理退格按钮点击
     */
    @FXML
    private void handleBackspace(ActionEvent event) {
        calculator.backspace();
        updateDisplay();
    }

    /**
     * 处理正负号切换
     */
    @FXML
    private void handleToggleSign(ActionEvent event) {
        calculator.toggleSign();
        updateDisplay();
    }

    /**
     * 处理百分比计算
     */
    @FXML
    private void handlePercentage(ActionEvent event) {
        calculator.percentage();
        updateDisplay();
    }

    /**
     * 更新显示屏
     */
    private void updateDisplay() {
        String display = calculator.getCurrentDisplay();
        displayField.setText(display);
        
        // 如果显示错误，添加错误样式
        if ("错误".equals(display)) {
            displayField.getStyleClass().add("error");
        } else {
            displayField.getStyleClass().remove("error");
        }
    }

    /**
     * 设置键盘事件处理
     */
    private void setupKeyboardHandling() {
        displayField.setOnKeyPressed(this::handleKeyPressed);
        displayField.setFocusTraversable(true);
        displayField.requestFocus();
    }

    /**
     * 处理键盘按键事件
     */
    private void handleKeyPressed(KeyEvent event) {
        String key = event.getCode().toString();
        
        switch (key) {
            // 数字键
            case "DIGIT0":
            case "NUMPAD0":
                calculator.inputDigit("0");
                break;
            case "DIGIT1":
            case "NUMPAD1":
                calculator.inputDigit("1");
                break;
            case "DIGIT2":
            case "NUMPAD2":
                calculator.inputDigit("2");
                break;
            case "DIGIT3":
            case "NUMPAD3":
                calculator.inputDigit("3");
                break;
            case "DIGIT4":
            case "NUMPAD4":
                calculator.inputDigit("4");
                break;
            case "DIGIT5":
            case "NUMPAD5":
                calculator.inputDigit("5");
                break;
            case "DIGIT6":
            case "NUMPAD6":
                calculator.inputDigit("6");
                break;
            case "DIGIT7":
            case "NUMPAD7":
                calculator.inputDigit("7");
                break;
            case "DIGIT8":
            case "NUMPAD8":
                calculator.inputDigit("8");
                break;
            case "DIGIT9":
            case "NUMPAD9":
                calculator.inputDigit("9");
                break;
                
            // 运算符
            case "PLUS":
            case "ADD":
                calculator.inputOperator("+");
                break;
            case "MINUS":
            case "SUBTRACT":
                calculator.inputOperator("-");
                break;
            case "MULTIPLY":
                calculator.inputOperator("×");
                break;
            case "DIVIDE":
                calculator.inputOperator("÷");
                break;
                
            // 小数点
            case "PERIOD":
            case "DECIMAL":
                calculator.inputDecimal();
                break;
                
            // 等号
            case "ENTER":
            case "EQUALS":
                calculator.calculate();
                break;
                
            // 退格
            case "BACK_SPACE":
                calculator.backspace();
                break;
                
            // 清除
            case "ESCAPE":
                calculator.clearAll();
                displayField.getStyleClass().remove("error");
                break;
                
            // 删除键作为清除当前输入
            case "DELETE":
                calculator.clearEntry();
                break;
        }
        
        updateDisplay();
        event.consume();
    }
}
