package com.calculator;

import com.calculator.model.Calculator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 计算器功能测试类
 */
public class CalculatorTest {

    private Calculator calculator;

    @BeforeEach
    void setUp() {
        calculator = new Calculator();
    }

    @Test
    void testBasicAddition() {
        calculator.inputDigit("5");
        calculator.inputOperator("+");
        calculator.inputDigit("3");
        calculator.calculate();
        assertEquals("8", calculator.getCurrentDisplay());
    }

    @Test
    void testBasicSubtraction() {
        calculator.inputDigit("10");
        calculator.inputOperator("-");
        calculator.inputDigit("4");
        calculator.calculate();
        assertEquals("6", calculator.getCurrentDisplay());
    }

    @Test
    void testBasicMultiplication() {
        calculator.inputDigit("6");
        calculator.inputOperator("×");
        calculator.inputDigit("7");
        calculator.calculate();
        assertEquals("42", calculator.getCurrentDisplay());
    }

    @Test
    void testBasicDivision() {
        calculator.inputDigit("15");
        calculator.inputOperator("÷");
        calculator.inputDigit("3");
        calculator.calculate();
        assertEquals("5", calculator.getCurrentDisplay());
    }

    @Test
    void testDivisionByZero() {
        calculator.inputDigit("5");
        calculator.inputOperator("÷");
        calculator.inputDigit("0");
        calculator.calculate();
        assertEquals("错误", calculator.getCurrentDisplay());
    }

    @Test
    void testDecimalInput() {
        calculator.inputDigit("3");
        calculator.inputDecimal();
        calculator.inputDigit("14");
        assertEquals("3.14", calculator.getCurrentDisplay());
    }

    @Test
    void testDecimalCalculation() {
        calculator.inputDigit("2");
        calculator.inputDecimal();
        calculator.inputDigit("5");
        calculator.inputOperator("+");
        calculator.inputDigit("1");
        calculator.inputDecimal();
        calculator.inputDigit("5");
        calculator.calculate();
        assertEquals("4", calculator.getCurrentDisplay());
    }

    @Test
    void testClearEntry() {
        calculator.inputDigit("123");
        calculator.clearEntry();
        assertEquals("0", calculator.getCurrentDisplay());
    }

    @Test
    void testClearAll() {
        calculator.inputDigit("5");
        calculator.inputOperator("+");
        calculator.inputDigit("3");
        calculator.clearAll();
        assertEquals("0", calculator.getCurrentDisplay());
    }

    @Test
    void testBackspace() {
        calculator.inputDigit("123");
        calculator.backspace();
        assertEquals("12", calculator.getCurrentDisplay());
        
        calculator.backspace();
        assertEquals("1", calculator.getCurrentDisplay());
        
        calculator.backspace();
        assertEquals("0", calculator.getCurrentDisplay());
    }

    @Test
    void testToggleSign() {
        calculator.inputDigit("5");
        calculator.toggleSign();
        assertEquals("-5", calculator.getCurrentDisplay());
        
        calculator.toggleSign();
        assertEquals("5", calculator.getCurrentDisplay());
    }

    @Test
    void testPercentage() {
        calculator.inputDigit("50");
        calculator.percentage();
        assertEquals("0.5", calculator.getCurrentDisplay());
    }

    @Test
    void testChainedOperations() {
        calculator.inputDigit("2");
        calculator.inputOperator("+");
        calculator.inputDigit("3");
        calculator.inputOperator("×");
        calculator.inputDigit("4");
        calculator.calculate();
        assertEquals("20", calculator.getCurrentDisplay());
    }

    @Test
    void testMemoryOperations() {
        // 测试内存存储
        calculator.inputDigit("10");
        calculator.memoryAdd();
        assertTrue(calculator.hasMemoryValue());
        
        // 测试内存读取
        calculator.clearAll();
        calculator.memoryRecall();
        assertEquals("10", calculator.getCurrentDisplay());
        
        // 测试内存加法
        calculator.inputDigit("5");
        calculator.memoryAdd();
        calculator.memoryRecall();
        assertEquals("15", calculator.getCurrentDisplay());
        
        // 测试内存减法
        calculator.inputDigit("3");
        calculator.memorySubtract();
        calculator.memoryRecall();
        assertEquals("12", calculator.getCurrentDisplay());
        
        // 测试内存清除
        calculator.memoryClear();
        assertFalse(calculator.hasMemoryValue());
    }

    @Test
    void testMultipleDecimalPoints() {
        calculator.inputDigit("3");
        calculator.inputDecimal();
        calculator.inputDigit("14");
        calculator.inputDecimal(); // 第二个小数点应该被忽略
        calculator.inputDigit("15");
        assertEquals("3.1415", calculator.getCurrentDisplay());
    }

    @Test
    void testLeadingZeros() {
        calculator.inputDigit("0");
        calculator.inputDigit("0");
        calculator.inputDigit("5");
        assertEquals("5", calculator.getCurrentDisplay());
    }
}
